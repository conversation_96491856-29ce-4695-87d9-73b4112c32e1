## 《炫酷电子风 & 赛博朋克风番茄时钟》开发需求文档

### 1. 产品目标

开发一个具备 **番茄工作法**（25 分钟专注 + 5 分钟休息）功能的桌面/网页应用，界面采用 **电子风（futuristic）+ 赛博朋克（cyberpunk）** 风格，提供流畅的动画与沉浸式体验，支持任务管理、计时提醒、数据统计。

---

### 2. 核心功能

#### 2.1 任务管理

* 添加、编辑、删除任务（任务标题、优先级、截止日期）
* 支持任务状态（未开始 / 进行中 / 已完成）
* 支持任务列表与番茄时钟联动（点击任务 → 开始计时）

#### 2.2 番茄计时器

* 25 分钟工作 + 5 分钟休息（可配置时间）
* 动态圆形进度条（霓虹光效动画）
* 倒计时结束后自动切换工作/休息模式
* 支持手动开始/暂停/重置
* 声音提示（科幻感提示音）

#### 2.3 数据统计

* 每日专注时间统计
* 番茄数量统计（今日 / 本周 / 本月）
* 任务完成率可视化（霓虹风格饼图/柱状图）

#### 2.4 自定义设置

* 主题切换（暗色电子风 / 霓虹赛博朋克）
* 声音包选择
* 番茄 & 休息时间自定义
* 数据本地存储（LocalStorage 或 IndexedDB）

---

### 3. 视觉风格（UI 设计）

#### 3.1 配色方案

* 主色：霓虹蓝 (#00FFFF)、赛博粉 (#FF00FF)、激光紫 (#8A2BE2)
* 辅助色：深黑 (#0A0A0A)、电子绿 (#39FF14)
* 渐变方案：霓虹蓝-紫、粉-蓝渐变背景

#### 3.2 字体

* 数字：LED 电子数字字体（如 Orbitron、Digital-7）
* 文本：无衬线科技字体（如 Exo 2、Rajdhani）

#### 3.3 动效

* 霓虹灯呼吸光效（CSS animation / WebGL shader）
* 倒计时圆环进度闪光动画
* 背景粒子流动（Canvas 粒子系统）
* 按钮点击波纹特效

---

### 4. 技术栈建议

* 前端框架：Vue 3 + Vite
* 样式：TailwindCSS + 自定义 CSS 动画
* 动画库：GSAP / Anime.js
* 图表：ECharts / Chart.js
* 音效：Howler.js
* 数据存储：LocalStorage / IndexedDB

---

### 5. 交互流程

1. 用户进入页面 → 看到当前任务列表
2. 选择任务 → 点击“开始专注”
3. 圆形霓虹倒计时启动 → 背景光效变化
4. 时间结束 → 提示音 + 自动切换到休息模式
5. 休息结束 → 再次提示音，返回工作模式
6. 数据实时记录 → 统计面板更新

---

### 6. AI 提示词模板（Cursor 可直接用）

```
你是一个专业的前端开发工程师，请用 Vue 3  开发一个炫酷的电子风 + 赛博朋克风格番茄时钟应用，需求如下：
1. 任务管理：支持添加/编辑/删除任务，点击任务可开始计时。
2. 番茄计时器：25分钟工作+5分钟休息，动态圆形霓虹进度条，支持开始/暂停/重置，时间可配置。任务开始后可以提前结束
3. 数据统计：显示每日专注时间、番茄数量、任务完成率（霓虹风格图表）。
4. 界面风格：深色背景，霓虹蓝、赛博粉、紫色渐变，数字字体为 Orbitron，添加霓虹呼吸光效动画，背景有粒子特效。
5. 技术栈：Vue 3 数据存储在 LocalStorage。
6. 动效：进度条有光效流动动画，按钮有波纹点击效果，背景粒子动态变化。
7. 响应式设计：适配 PC 和移动端。

```
