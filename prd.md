# 炫酷电子风赛博朋克番茄时钟应用 - 产品需求文档 (PRD)

## 1. 项目概述

### 1.1 产品定位
基于番茄工作法的时间管理应用，采用电子风+赛博朋克视觉风格，提供沉浸式专注体验。

### 1.2 技术栈
- **前端框架**: Vue 3 + TypeScript + Vite
- **样式方案**: TailwindCSS + 自定义CSS动画
- **动画库**: GSAP
- **图表库**: ECharts
- **音效库**: Howler.js
- **数据存储**: LocalStorage + Pinia状态管理
- **构建工具**: Vite + ESLint + Prettier

### 1.3 目录结构
```
src/
├── components/          # 组件目录
│   ├── Timer/          # 计时器组件
│   ├── TaskList/       # 任务列表组件
│   ├── Statistics/     # 统计组件
│   └── Settings/       # 设置组件
├── stores/             # Pinia状态管理
├── utils/              # 工具函数
├── assets/             # 静态资源
├── styles/             # 样式文件
└── types/              # TypeScript类型定义
```

## 2. 核心功能模块

### 2.1 任务管理模块 (TaskManager)

#### 2.1.1 数据结构
```typescript
interface Task {
  id: string;
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in-progress' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  deadline?: Date;
  pomodoroCount: number;
  estimatedPomodoros: number;
}
```

#### 2.1.2 核心功能
- **CRUD操作**: 创建、读取、更新、删除任务
- **任务排序**: 按优先级、创建时间、截止日期排序
- **任务筛选**: 按状态、优先级筛选
- **任务搜索**: 支持标题模糊搜索

#### 2.1.3 组件设计
```
TaskList.vue
├── TaskItem.vue
├── TaskForm.vue (新增/编辑)
├── TaskFilter.vue
└── TaskSearch.vue
```

### 2.2 番茄计时器模块 (PomodoroTimer)

#### 2.2.1 数据结构
```typescript
interface TimerConfig {
  workDuration: number;    // 工作时长(分钟)
  shortBreak: number;      // 短休息时长(分钟)
  longBreak: number;       // 长休息时长(分钟)
  longBreakInterval: number; // 长休息间隔(番茄数)
}

interface TimerState {
  currentTime: number;     // 当前剩余时间(秒)
  totalTime: number;       // 总时间(秒)
  isRunning: boolean;      // 是否运行中
  isPaused: boolean;       // 是否暂停
  mode: 'work' | 'short-break' | 'long-break';
  currentTask: Task | null;
  pomodoroCount: number;   // 当前番茄数
}
```

#### 2.2.2 核心功能
- **计时控制**: 开始、暂停、重置、停止
- **模式切换**: 工作模式 ↔ 休息模式自动切换
- **进度显示**: 圆形进度条 + 数字倒计时
- **声音提醒**: 开始、结束、切换模式提示音
- **任务关联**: 与选中任务绑定

#### 2.2.3 组件设计
```
Timer.vue
├── CircularProgress.vue  # 圆形进度条
├── TimerControls.vue     # 控制按钮
├── TimerDisplay.vue      # 时间显示
└── TimerSettings.vue     # 时间设置
```

### 2.3 数据统计模块 (Statistics)

#### 2.3.1 数据结构
```typescript
interface DailyStats {
  date: string;
  focusTime: number;        // 专注时间(分钟)
  pomodoroCount: number;    // 番茄数量
  tasksCompleted: number;   // 完成任务数
  tasksTotal: number;       // 总任务数
}

interface WeeklyStats {
  week: string;
  dailyStats: DailyStats[];
  totalFocusTime: number;
  totalPomodoros: number;
  completionRate: number;
}
```

#### 2.3.2 核心功能
- **时间统计**: 日/周/月专注时间统计
- **番茄统计**: 番茄数量趋势图
- **完成率**: 任务完成率饼图
- **效率分析**: 专注效率热力图

#### 2.3.3 组件设计
```
Statistics.vue
├── TimeChart.vue         # 时间统计图表
├── PomodoroChart.vue     # 番茄统计图表
├── CompletionChart.vue   # 完成率图表
└── EfficiencyHeatmap.vue # 效率热力图
```

## 3. 视觉设计规范

### 3.1 设计令牌 (Design Tokens)
```css
:root {
  /* 主色调 */
  --neon-blue: #00FFFF;
  --cyber-pink: #FF00FF;
  --laser-purple: #8A2BE2;
  --electric-green: #39FF14;
  
  /* 背景色 */
  --bg-primary: #0A0A0A;
  --bg-secondary: #1A1A1A;
  --bg-tertiary: #2A2A2A;
  
  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #00FFFF, #8A2BE2);
  --gradient-secondary: linear-gradient(135deg, #FF00FF, #00FFFF);
  
  /* 字体 */
  --font-digital: 'Orbitron', monospace;
  --font-tech: 'Exo 2', sans-serif;
  
  /* 动画时长 */
  --animation-fast: 0.2s;
  --animation-normal: 0.4s;
  --animation-slow: 0.8s;
}
```

### 3.2 组件样式规范
- **按钮**: 霓虹边框 + 悬停光效 + 点击波纹
- **输入框**: 发光边框 + 聚焦动画
- **进度条**: 渐变填充 + 流光动画
- **卡片**: 半透明背景 + 霓虹边框

### 3.3 动画效果
- **霓虹呼吸**: `animation: neon-pulse 2s ease-in-out infinite alternate`
- **流光效果**: `animation: flowing-light 3s linear infinite`
- **粒子背景**: Canvas实现的动态粒子系统
- **页面切换**: 滑动 + 淡入淡出

## 4. 状态管理设计

### 4.1 Store结构
```typescript
// stores/index.ts
export const useTaskStore = defineStore('tasks', {
  state: () => ({
    tasks: [] as Task[],
    currentTask: null as Task | null,
    filter: 'all' as TaskFilter,
    searchQuery: ''
  }),
  actions: {
    addTask, updateTask, deleteTask, setCurrentTask
  }
});

export const useTimerStore = defineStore('timer', {
  state: () => ({
    config: defaultTimerConfig,
    state: defaultTimerState,
    history: [] as TimerSession[]
  }),
  actions: {
    startTimer, pauseTimer, resetTimer, updateConfig
  }
});

export const useStatsStore = defineStore('stats', {
  state: () => ({
    dailyStats: [] as DailyStats[],
    weeklyStats: [] as WeeklyStats[]
  }),
  actions: {
    recordSession, getDailyStats, getWeeklyStats
  }
});
```

## 5. 核心算法与逻辑

### 5.1 计时器核心逻辑
```typescript
class PomodoroTimer {
  private intervalId: number | null = null;
  
  start() {
    this.intervalId = setInterval(() => {
      if (this.state.currentTime > 0) {
        this.state.currentTime--;
        this.updateProgress();
      } else {
        this.handleTimerComplete();
      }
    }, 1000);
  }
  
  private handleTimerComplete() {
    this.playNotificationSound();
    this.recordSession();
    this.switchMode();
  }
  
  private switchMode() {
    // 工作模式 → 休息模式切换逻辑
    // 长休息间隔判断逻辑
  }
}
```

### 5.2 数据持久化
```typescript
class StorageManager {
  save<T>(key: string, data: T): void {
    localStorage.setItem(key, JSON.stringify(data));
  }
  
  load<T>(key: string, defaultValue: T): T {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  }
}
```

## 6. 性能优化策略

### 6.1 代码分割
- 路由级别代码分割
- 组件懒加载
- 第三方库按需引入

### 6.2 动画优化
- 使用CSS transform替代position变化
- 利用will-change属性
- 合理使用requestAnimationFrame

### 6.3 数据优化
- 虚拟滚动处理大量任务
- 防抖处理搜索输入
- 缓存计算结果

## 7. 开发里程碑

### Phase 1: 基础功能 (Week 1-2)
- [ ] 项目初始化与环境配置
- [ ] 基础UI组件开发
- [ ] 任务CRUD功能
- [ ] 基础计时器功能

### Phase 2: 核心功能 (Week 3-4)
- [ ] 完整番茄工作法逻辑
- [ ] 数据统计功能
- [ ] 本地数据持久化
- [ ] 基础动画效果

### Phase 3: 视觉优化 (Week 5-6)
- [ ] 赛博朋克视觉风格
- [ ] 高级动画效果
- [ ] 粒子背景系统
- [ ] 音效集成

### Phase 4: 完善优化 (Week 7-8)
- [ ] 响应式设计
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 测试与调试

## 8. 验收标准

### 8.1 功能验收
- 所有核心功能正常运行
- 数据持久化正确
- 跨浏览器兼容性

### 8.2 性能验收
- 首屏加载时间 < 2s
- 动画帧率 > 60fps
- 内存使用合理

### 8.3 视觉验收
- 符合设计规范
- 动画效果流畅
- 响应式适配完整
